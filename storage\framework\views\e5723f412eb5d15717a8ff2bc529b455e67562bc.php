

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Item Details</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Item::class)): ?>
            <a href="<?php echo e(route('items.index')); ?>" class="btn btn-outline-primary me-2">
                <i class="bi bi-arrow-left me-1"></i>
                Back to Items
            </a>
            <?php endif; ?>

            <?php if(!$item->closed_by): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Item::class)): ?>
            <a href="<?php echo e(route('items.edit', $item)); ?>" class="btn btn-primary">
                <i class="bi bi-pencil-square me-1"></i>
                Edit Item
            </a>
            <?php endif; ?>
            <?php endif; ?>
            </div>
        </div>
    </div>


    <div class="row">
        <!-- Item Images Section -->
        <div class="col-lg-5 mb-4 mb-lg-0">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Item Images</h5>
                </div>
                <div class="card-body">
                    <!-- Main Image -->
                    <?php if($item->getMedia('media')->count() > 0): ?>
                        <div class="mb-4 text-center">
                            <a href="<?php echo e($item->getMedia('media')->first()->getUrl('image')); ?>" data-fslightbox="gallery">
                                <img class="img-fluid rounded border"
                                     src="<?php echo e($item->getMedia('media')->first()->getUrl('image')); ?>"
                                     alt="<?php echo e($item->name); ?>"
                                     style="max-height: 300px; object-fit: contain;">
                            </a>
                        </div>

                        <!-- Thumbnails -->
                        <div class="row">
                            <?php $__currentLoopData = $item->getMedia('media'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-3 mb-3">
                                    <a href="<?php echo e($file->getUrl('image')); ?>" data-fslightbox="gallery">
                                        <img class="img-fluid rounded border"
                                             src="<?php echo e($file->getUrl('cropped')); ?>"
                                             alt="Image Description"
                                             style="width: 100%; height: 80px; object-fit: cover;">
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-2 text-muted">No images available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Item Details Section -->
        <div class="col-lg-7">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Item Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12 mb-3">
                            <h4 class="fw-bold"><?php echo e($item->name ?? '-'); ?></h4>
                            <span class="badge bg-primary"><?php echo e(optional($item->auctionType)->name ?? '-'); ?></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-1">Current Bid</h6>
                            <h5 class="fw-bold"><?php echo e($item->bid_amount ?? 'No bids yet'); ?></h5>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-1">Reference Number</h6>
                            <p><?php echo e($item->reference_number ?? '-'); ?></p>
                        </div>

                        <div class="col-md-12">
                            <h6 class="text-muted mb-1">Description</h6>
                            <div class="p-3 bg-light rounded">
                                <p class="mb-0"><?php echo e($item->description ?? 'No description available'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Details Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Additional Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-1">Owner</h6>
                            <p><?php echo e(optional($item->user)->name ?? '-'); ?></p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-1">Status</h6>
                            <p><?php echo e(optional($item->status)->name ?? '-'); ?></p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-1">Date From</h6>
                            <p><?php echo e($item->date_from ?? '-'); ?></p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-1">Date To</h6>
                            <p><?php echo e($item->date_to ?? '-'); ?></p>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-muted mb-1">Target Amount</h6>
                            <p><?php echo e($item->target_amount ? number_format($item->target_amount, 2) : '-'); ?></p>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-muted mb-1">Date Added</h6>
                            <p><?php echo e($item->created_at ? $item->created_at->format('M d, Y') : '-'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <div class="d-flex gap-2">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Item::class)): ?>
            <a href="<?php echo e(route('items.create')); ?>" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i>
                <?php echo app('translator')->get('crud.common.create'); ?>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $item)): ?>
            <a href="<?php echo e(route('items.index')); ?>" class="btn btn-outline-secondary">
                <i class="bi bi-list me-1"></i>
                View All Items
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/fslightbox@3.4.1/index.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof refreshFsLightbox === 'function') {
            refreshFsLightbox();
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/items/show.blade.php ENDPATH**/ ?>